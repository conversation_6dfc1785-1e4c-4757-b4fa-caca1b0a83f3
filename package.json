{"name": "focal-finance-tracker", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host 0.0.0.0 --port 3000", "dev:worker": "wrangler dev --local --port 8787", "dev:full": "concurrently \"pnpm dev\" \"pnpm dev:worker\"", "build": "vite build", "lint": "eslint --cache -f json --quiet .", "preview": "bun run build && vite preview --host 0.0.0.0 --port ${PORT:-4173}", "deploy": "pnpm run build && wrangler deploy", "db:migrate": "wrangler d1 execute focal_expensi_db --local --file=./migrations/001_initial_schema.sql", "db:migrate:prod": "wrangler d1 execute focal_expensi_db --remote --file=./migrations/001_initial_schema.sql", "db:migrate:002": "wrangler d1 execute focal_expensi_db --local --file=./migrations/002_quantity_real.sql", "db:migrate:002:prod": "wrangler d1 execute focal_expensi_db --remote --file=./migrations/002_quantity_real.sql", "setup:prod": "bash scripts/setup-production.sh"}, "dependencies": {"@getbrevo/brevo": "^3.0.1", "@google/generative-ai": "^0.24.1", "@headlessui/react": "^2.2.4", "@hono/node-server": "^1.19.5", "@hookform/resolvers": "^5.1.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@tanstack/react-query": "^5.83.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "embla-carousel-react": "^8.6.0", "framer-motion": "^12.23.0", "hono": "^4.9.9", "immer": "^10.1.1", "input-otp": "^1.4.2", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.525.0", "motion": "^12.23.22", "next-themes": "^0.4.6", "react": "^18.3.1", "react-day-picker": "^9.8.0", "react-dom": "^18.3.1", "react-hook-form": "^7.60.0", "react-hotkeys-hook": "^5.1.0", "react-resizable-panels": "^3.0.6", "react-router-dom": "6.30.0", "react-select": "^5.10.2", "react-use": "^17.6.0", "react-webcam": "^7.2.0", "recharts": "2.15.4", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "vaul": "^1.1.2", "workbox-window": "^7.3.0", "zod": "^4.0.5", "zustand": "^5.0.6"}, "devDependencies": {"@cloudflare/workers-types": "^4.20251004.0", "@eslint/js": "^9.22.0", "@types/bcryptjs": "^3.0.0", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^22.15.3", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.1", "@types/react-webcam": "^3.0.0", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "@vite-pwa/assets-generator": "^1.0.1", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.21", "concurrently": "^9.2.1", "eslint": "^9.31.0", "eslint-import-resolver-typescript": "^4.4.4", "eslint-plugin-import": "^2.32.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "typescript": "5.8", "typescript-eslint": "^8.26.1", "vite": "^6.3.1", "vite-plugin-pwa": "^1.0.3", "wrangler": "^4.42.0"}}